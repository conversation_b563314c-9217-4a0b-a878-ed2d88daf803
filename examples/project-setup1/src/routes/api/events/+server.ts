// Centralized event notification endpoint
// Handles sending single messages to external services (Discord, etc.)

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { env } from '$env/dynamic/private';

export const POST: RequestHandler = async ({ request }) => {
    try {
        const { message, embed, webhookUrl } = await request.json();

        // Validate required data
        if (!message && !embed) {
            return json({
                success: false,
                error: 'Either message or embed is required'
            }, { status: 400 });
        }

        // Get webhook URL from parameter or environment
        const targetWebhookUrl = webhookUrl || env.DISCORD_WEBHOOK_URL;

        if (!targetWebhookUrl) {
            console.warn('⚠️ No webhook URL configured for events');
            return json({
                success: false,
                error: 'No webhook URL configured'
            }, { status: 400 });
        }

        // Prepare payload for Discord
        const payload: any = {};

        if (message) {
            payload.content = message;
        }

        if (embed) {
            payload.embeds = [embed];
        }

        // Send to Discord webhook
        const response = await fetch(targetWebhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            console.error(`❌ Webhook failed: ${response.status} ${response.statusText}`);
            return json({
                success: false,
                error: `Webhook request failed: ${response.status}`
            }, { status: 500 });
        }

        console.log('📢 Event notification sent successfully');

        return json({
            success: true,
            message: 'Event notification sent'
        });

    } catch (error) {
        console.error('❌ Events endpoint error:', error);
        return json({
            success: false,
            error: 'Failed to send event notification'
        }, { status: 500 });
    }
};
