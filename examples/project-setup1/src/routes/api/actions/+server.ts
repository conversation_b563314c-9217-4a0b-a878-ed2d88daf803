// Server endpoint for tracking actions (batch processing)
// Handles PocketBase operations for actions and navigations
// Discord notifications are handled separately on client-side

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import PocketBase from 'pocketbase';
import { POCKETBASE_URL } from '$env/static/private';

// Initialize PocketBase client
const pb = new PocketBase(POCKETBASE_URL || 'http://127.0.0.1:8090');

export const POST: RequestHandler = async ({ request }) => {
    try {
        const { actions, navigations } = await request.json();
        console.log(`📥 Received ${actions?.length || 0} actions, ${navigations?.length || 0} navigations`);

        // Early return if no data
        if ((!actions || actions.length === 0) && (!navigations || navigations.length === 0)) {
            return json({
                success: true,
                processed: { actions: 0, navigations: 0 }
            });
        }

        let processedActions = 0;
        let processedNavigations = 0;

        // Process actions with error isolation
        if (actions && actions.length > 0) {
            for (const action of actions) {
                try {
                    // Check if action already exists for this user
                    const existingActions = await pb.collection('tracking_actions').getList(1, 1, {
                        filter: `user_id = "${action.user_id}" && action_name = "${action.action_name}"`
                    });

                    if (existingActions.items.length > 0) {
                        // Update existing action count
                        const existing = existingActions.items[0];
                        await pb.collection('tracking_actions').update(existing.id, {
                            count: (existing.count || 1) + 1,
                            last_executed: action.timestamp,
                            page_path: action.page_path // Update current page
                        });
                        console.log(`🔄 Updated action: ${action.action_name} (count: ${(existing.count || 1) + 1})`);
                    } else {
                        // Create new action record
                        await pb.collection('tracking_actions').create({
                            user_id: action.user_id,
                            action_name: action.action_name,
                            page_path: action.page_path,
                            count: 1,
                            first_executed: action.timestamp,
                            last_executed: action.timestamp
                        });
                        console.log(`✨ Created new action: ${action.action_name}`);
                    }
                    processedActions++;
                } catch (actionError) {
                    console.error(`❌ Failed to process action ${action.action_name}:`, actionError);
                    // Continue processing other actions
                }
            }
        }

        // Process navigations with error isolation
        if (navigations && navigations.length > 0) {
            for (const navigation of navigations) {
                try {
                    await pb.collection('tracking_navigation').create({
                        user_id: navigation.user_id,
                        page_path: navigation.page_path,
                        time_spent: navigation.time_spent,
                        entered_at: navigation.entered_at,
                        exited_at: navigation.exited_at
                    });
                    console.log(`📄 Saved navigation: ${navigation.page_path} (${navigation.time_spent}ms)`);
                    processedNavigations++;
                } catch (navError) {
                    console.error(`❌ Failed to process navigation ${navigation.page_path}:`, navError);
                    // Continue processing other navigations
                }
            }
        }

        return json({
            success: true,
            processed: {
                actions: processedActions,
                navigations: processedNavigations
            }
        });

    } catch (error) {
        console.error('❌ Tracking endpoint error:', error);

        // Return success even if PocketBase fails to prevent blocking
        return json({
            success: true,
            error: 'Database unavailable - data discarded',
            processed: { actions: 0, navigations: 0 }
        });
    }
};
