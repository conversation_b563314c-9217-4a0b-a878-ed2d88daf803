<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import Icon from '$lib/components/ui/icon.svelte';
	import { reportError, getErrorReporter, ErrorType, initErrorReporting } from '$lib/reports';
	import { devErrorBoundary } from '$lib/reports/boundary';
	import { toast } from 'svelte-sonner';

	// Exemplo de dados para demonstração
	let errorCount = 0;
	let formData = { name: '', email: '' };

	// Funções de exemplo para demonstrar o error reporting
	async function handleHttpError() {
		errorCount++;

		// Simula um erro HTTP comum
		await reportError(
			ErrorType.HTTP_ERROR,
			{
				functionName: 'handleHttpError',
				message: `Simulated API call failed - attempt ${errorCount}`,
				statusCode: 500
			},
			true
		); // force = true para funcionar em dev

		toast.error('Erro HTTP simulado e reportado!');
	}

	async function handleComponentError() {
		// Simula um erro de componente
		await reportError(
			ErrorType.COMPONENT_ERROR,
			{
				functionName: 'UserProfileCard',
				message: 'Cannot read property "name" of undefined - user object is null',
				originalError: new Error('TypeError: Cannot read property "name" of undefined')
			},
			true
		);

		toast.error('Erro de componente simulado e reportado!');
	}

	async function handleTimeoutError() {
		// Simula um erro de timeout
		await reportError(
			ErrorType.TIMEOUT_ERROR,
			{
				functionName: 'loadLargeDataset',
				message: 'Operation timed out after 30 seconds while loading user reports'
			},
			true
		);

		toast.error('Erro de timeout simulado e reportado!');
	}

	function handleFatalError() {
		// Este erro será capturado automaticamente pelos handlers globais
		setTimeout(() => {
			throw new Error('Simulated fatal error - this will be auto-reported!');
		}, 100);

		toast.error('Erro fatal será disparado em 100ms!');
	}

	async function handleApiCallExample() {
		try {
			// Simula uma chamada de API que pode falhar
			const response = await fetch('/api/nonexistent-endpoint');

			if (!response.ok) {
				await reportError(
					ErrorType.HTTP_ERROR,
					{
						functionName: 'handleApiCallExample',
						message: `API call failed: ${response.status} ${response.statusText}`,
						statusCode: response.status
					},
					true
				);

				throw new Error(`API call failed: ${response.status}`);
			}

			return await response.json();
		} catch (error) {
			// Erro de rede ou parsing
			await reportError(
				ErrorType.HTTP_ERROR,
				{
					functionName: 'handleApiCallExample',
					message: `Network error: ${error instanceof Error ? error.message : String(error)}`,
					originalError: error instanceof Error ? error : new Error(String(error))
				},
				true
			);

			toast.error('Erro de API real capturado e reportado!');
		}
	}

	function handleFormValidationError() {
		// Simula erro de validação de formulário
		if (!formData.name.trim()) {
			reportError(
				ErrorType.COMPONENT_ERROR,
				{
					functionName: 'validateForm',
					message: 'Form validation failed: Name field is required'
				},
				true
			);

			toast.error('Erro de validação reportado!');
			return;
		}

		toast.success('Formulário válido!');
	}

	function showErrorReporterInfo() {
		try {
			const reporter = getErrorReporter();
			console.log('📊 Error Reporter Instance:', reporter);
			console.log('🔧 Reporter está ativo e funcionando!');

			toast.success('Informações do reporter exibidas no console!');
		} catch (error) {
			console.error('Error reporter não inicializado:', error);
			toast.error('Error reporter não está inicializado!');
		}
	}

	// Função que vai gerar erro para testar error boundary
	function triggerBoundaryError() {
		throw new Error('Error boundary test - this error should be caught!');
	}

	$effect(() => {
		initErrorReporting();
	});
</script>

<svelte:head>
	<title>Error Reporting System Demo | MeuApp</title>
	<meta
		name="description"
		content="Demonstração e exemplos do sistema de error reporting integrado"
	/>
</svelte:head>

<div class="container mx-auto px-4 py-8">
	<div class="mx-auto max-w-6xl">
		<!-- Header -->
		<div class="mb-8">
			<h1 class="mb-4 flex items-center gap-3 text-4xl font-bold">
				<Icon icon="mdi:alert-circle" class="text-red-600" />
				Sistema de Error Reporting
			</h1>
			<p class="text-muted-foreground text-lg">
				Demonstração e exemplos práticos do sistema de error reporting integrado ao seu projeto.
			</p>
		</div>

		<!-- Status do Sistema -->
		<Card.Root class="mb-8 border-green-200 bg-green-50">
			<Card.Header>
				<Card.Title class="flex items-center gap-2 text-green-800">
					<Icon icon="mdi:check-circle" />
					Sistema Ativo
				</Card.Title>
			</Card.Header>
			<Card.Content>
				<p class="text-green-700">
					O error reporting system está funcionando automaticamente. Todos os erros desta página
					estão sendo reportados para Discord.
				</p>
				<p class="mt-2 text-sm text-green-600">
					💡 Em desenvolvimento, os erros são logados no console. Use force=true para testar o
					Discord.
				</p>
			</Card.Content>
		</Card.Root>

		<!-- Exemplos Interativos -->
		<div class="mb-8 grid gap-6 md:grid-cols-2 lg:grid-cols-3">
			<!-- HTTP Error -->
			<Card.Root>
				<Card.Header>
					<Card.Title class="flex items-center gap-2">
						<Icon icon="mdi:web" class="text-red-500" />
						HTTP Error
					</Card.Title>
				</Card.Header>
				<Card.Content class="space-y-4">
					<p class="text-muted-foreground text-sm">
						Simula erros de API calls, requests que falharam, etc.
					</p>
					<Button onclick={handleHttpError} variant="destructive" class="w-full">
						🔴 Simular HTTP Error ({errorCount})
					</Button>
					<code class="block rounded bg-gray-100 p-2 text-xs">
						reportError('HTTP_ERROR', details)
					</code>
				</Card.Content>
			</Card.Root>

			<!-- Component Error -->
			<Card.Root>
				<Card.Header>
					<Card.Title class="flex items-center gap-2">
						<Icon icon="mdi:puzzle" class="text-orange-500" />
						Component Error
					</Card.Title>
				</Card.Header>
				<Card.Content class="space-y-4">
					<p class="text-muted-foreground text-sm">
						Erros de componentes Svelte, problemas de rendering, etc.
					</p>
					<Button onclick={handleComponentError} variant="outline" class="w-full">
						🟡 Simular Component Error
					</Button>
					<code class="block rounded bg-gray-100 p-2 text-xs">
						reportError('COMPONENT_ERROR', details)
					</code>
				</Card.Content>
			</Card.Root>

			<!-- Timeout Error -->
			<Card.Root>
				<Card.Header>
					<Card.Title class="flex items-center gap-2">
						<Icon icon="mdi:clock-alert" class="text-orange-600" />
						Timeout Error
					</Card.Title>
				</Card.Header>
				<Card.Content class="space-y-4">
					<p class="text-muted-foreground text-sm">Operações que excedem limites de tempo.</p>
					<Button onclick={handleTimeoutError} variant="secondary" class="w-full">
						🟠 Simular Timeout Error
					</Button>
					<code class="block rounded bg-gray-100 p-2 text-xs">
						reportError('TIMEOUT_ERROR', details)
					</code>
				</Card.Content>
			</Card.Root>
		</div>

		<!-- Fatal Error e API Real -->
		<div class="mb-8 grid gap-6 md:grid-cols-2">
			<!-- Fatal Error -->
			<Card.Root>
				<Card.Header>
					<Card.Title class="flex items-center gap-2">
						<Icon icon="mdi:skull" class="text-red-700" />
						Fatal Error (Auto-capturado)
					</Card.Title>
				</Card.Header>
				<Card.Content class="space-y-4">
					<p class="text-muted-foreground text-sm">
						Erros não tratados são capturados automaticamente pelos handlers globais.
					</p>
					<Button onclick={handleFatalError} variant="destructive" class="w-full">
						💀 Disparar Fatal Error
					</Button>
					<code class="block rounded bg-gray-100 p-2 text-xs">
						// Auto-capturado por window.onerror
					</code>
				</Card.Content>
			</Card.Root>

			<!-- API Call Real -->
			<Card.Root>
				<Card.Header>
					<Card.Title class="flex items-center gap-2">
						<Icon icon="mdi:api" class="text-blue-500" />
						API Call Real
					</Card.Title>
				</Card.Header>
				<Card.Content class="space-y-4">
					<p class="text-muted-foreground text-sm">
						Teste com uma chamada de API real que vai falhar.
					</p>
					<Button onclick={handleApiCallExample} variant="outline" class="w-full">
						🌐 Testar API Real
					</Button>
					<code class="block rounded bg-gray-100 p-2 text-xs">
						fetch('/api/nonexistent-endpoint')
					</code>
				</Card.Content>
			</Card.Root>
		</div>

		<!-- Error Boundary Demo -->
		<Card.Root class="mb-8">
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Icon icon="mdi:shield-alert" />
					Error Boundary Demo
				</Card.Title>
			</Card.Header>
			<Card.Content>
				<p class="text-muted-foreground mb-4 text-sm">
					Error boundaries capturam erros automaticamente em componentes. Teste clicando no botão
					abaixo:
				</p>

				<!-- Área com error boundary -->
				<div
					use:devErrorBoundary={{ componentName: 'ErrorBoundaryDemo' }}
					class="rounded-lg border p-4"
				>
					<h4 class="mb-2 font-semibold">Área Protegida por Error Boundary</h4>
					<p class="text-muted-foreground mb-4 text-sm">
						Este botão vai gerar um erro que será capturado automaticamente:
					</p>
					<Button onclick={triggerBoundaryError} variant="destructive">
						🛡️ Testar Error Boundary
					</Button>
				</div>

				<code class="mt-4 block rounded bg-gray-100 p-2 text-xs">
					&lt;div use:devErrorBoundary={`{ componentName: 'MyComponent' }`}&gt;
				</code>
			</Card.Content>
		</Card.Root>

		<!-- Formulário com Validação -->
		<Card.Root class="mb-8">
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Icon icon="mdi:form-select" />
					Validação de Formulário
				</Card.Title>
			</Card.Header>
			<Card.Content>
				<p class="text-muted-foreground mb-4 text-sm">
					Exemplo de como reportar erros de validação de formulário:
				</p>
				<div class="grid gap-4 md:grid-cols-2">
					<div>
						<label for="demo-name" class="mb-1 block text-sm font-medium">Nome *</label>
						<input
							id="demo-name"
							type="text"
							bind:value={formData.name}
							class="w-full rounded-md border px-3 py-2"
							placeholder="Digite seu nome"
						/>
					</div>
					<div>
						<label for="demo-email" class="mb-1 block text-sm font-medium">Email</label>
						<input
							id="demo-email"
							type="email"
							bind:value={formData.email}
							class="w-full rounded-md border px-3 py-2"
							placeholder="<EMAIL>"
						/>
					</div>
				</div>
				<Button onclick={handleFormValidationError} class="mt-4">Validar Formulário</Button>
			</Card.Content>
		</Card.Root>

		<!-- Debug e Informações -->
		<Card.Root class="mb-8">
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Icon icon="mdi:bug" />
					Debug e Informações
				</Card.Title>
			</Card.Header>
			<Card.Content class="space-y-4">
				<p class="text-muted-foreground text-sm">
					Visualize informações do sistema de error reporting.
				</p>
				<Button onclick={showErrorReporterInfo} variant="outline">
					<Icon icon="mdi:console" class="mr-2" />
					Mostrar Info do Reporter
				</Button>
			</Card.Content>
		</Card.Root>

		<!-- Documentação -->
		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Icon icon="mdi:book-open" />
					Documentação e Próximos Passos
				</Card.Title>
			</Card.Header>
			<Card.Content class="space-y-4">
				<div class="grid gap-4 md:grid-cols-2">
					<div>
						<h4 class="mb-2 font-semibold">📚 Documentação Completa</h4>
						<p class="text-muted-foreground mb-2 text-sm">Consulte o README completo em:</p>
						<code class="block rounded bg-gray-100 p-2 text-xs"> src/lib/reports/README.md </code>
					</div>
					<div>
						<h4 class="mb-2 font-semibold">🔧 Configuração</h4>
						<p class="text-muted-foreground mb-2 text-sm">Configure o Discord webhook em:</p>
						<code class="block rounded bg-gray-100 p-2 text-xs">
							.env → VITE_DISCORD_WEBHOOK_URL
						</code>
					</div>
				</div>

				<div class="mt-6 rounded-lg bg-red-50 p-4">
					<h4 class="mb-2 font-semibold text-red-800">🚨 Tipos de Erro Suportados</h4>
					<div class="grid gap-2 text-sm text-red-700 md:grid-cols-2">
						<div>• 🔴 <strong>HTTP_ERROR</strong> - API calls, network requests</div>
						<div>• 🟡 <strong>COMPONENT_ERROR</strong> - Svelte component errors</div>
						<div>• 🟠 <strong>TIMEOUT_ERROR</strong> - Operations that exceed time limits</div>
						<div>• 💀 <strong>FATAL_ERROR</strong> - Unhandled errors (auto-captured)</div>
					</div>
				</div>

				<div class="mt-4 rounded-lg bg-blue-50 p-4">
					<h4 class="mb-2 font-semibold text-blue-800">💡 Dicas Importantes</h4>
					<ul class="space-y-1 text-sm text-blue-700">
						<li>• Configure o webhook do Discord para receber notificações</li>
						<li>• Use force=true em desenvolvimento para testar o Discord</li>
						<li>• Error boundaries capturam erros automaticamente</li>
						<li>• Sistema agrupa erros similares em janelas de 5 minutos</li>
						<li>• Cleanup automático previne memory leaks</li>
					</ul>
				</div>
			</Card.Content>
		</Card.Root>
	</div>
</div>
