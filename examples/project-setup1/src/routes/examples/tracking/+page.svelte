<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import Icon from '$lib/components/ui/icon.svelte';
	import { initTracking, trackAction } from '$lib/tracking';
	import { toast } from 'svelte-sonner';

	// Exemplo de dados para demonstração
	let clickCount = $state(0);
	let formData = $state({ name: '', email: '' });

	// Funções de exemplo para demonstrar o tracking
	function handleBasicTracking() {
		clickCount++;
		trackAction('demo-basic-button-click');
		toast.success('Ação básica rastreada! Verifique o console.');
	}

	function handleFeatureUsage() {
		trackAction('demo-feature-usage');
		toast.success('Uso de feature rastreado!');
	}

	function handleFormSubmit() {
		trackAction('demo-form-submit');
		toast.success('Submissão de formulário rastreada!');
	}

	function handleFormInput() {
		trackAction('demo-form-input');
	}

	function handleDiscordNotification() {
		trackAction('demo-discord-notification', {
			discord: true,
			discordMessage: '🎯 Demo Discord notification from tracking page!'
		});
		toast.success('Ação com Discord enviada! Verifique o Discord se configurado.');
	}

	function handleImportantAction() {
		trackAction('demo-important-action', {
			discord: true,
			discordMessage: '🚨 Important demo action performed!'
		});
		toast.success('Ação importante rastreada com Discord!');
	}

	function handleUserSignup() {
		trackAction('demo-user-signup', {
			discord: true,
			discordMessage: '🎉 New demo user signed up!'
		});
		toast.success('Simulação de signup com Discord!');
	}

	$effect(() => {
		initTracking({
			projectId: 'meu-projeto',
			apiUrl: '/api/actions'
		});
	});
</script>

<svelte:head>
	<title>Tracking System Demo | MeuApp</title>
	<meta name="description" content="Demonstração e exemplos do sistema de tracking integrado" />
</svelte:head>

<div class="container mx-auto px-4 py-8">
	<div class="mx-auto max-w-6xl">
		<!-- Header -->
		<div class="mb-8">
			<h1 class="mb-4 flex items-center gap-3 text-4xl font-bold">
				<Icon icon="mdi:chart-line" class="text-blue-600" />
				Simple Tracking System
			</h1>
			<p class="text-muted-foreground text-lg">
				Demonstração da nova API simplificada de tracking com suporte a Discord.
			</p>
		</div>

		<!-- Status do Sistema -->
		<Card.Root class="mb-8 border-green-200 bg-green-50">
			<Card.Header>
				<Card.Title class="flex items-center gap-2 text-green-800">
					<Icon icon="mdi:check-circle" />
					Sistema Ativo
				</Card.Title>
			</Card.Header>
			<Card.Content>
				<p class="text-green-700">
					O sistema de tracking simplificado está ativo. Navegação é automática, ações são
					rastreadas via trackAction().
				</p>
			</Card.Content>
		</Card.Root>

		<!-- Exemplos Interativos -->
		<div class="mb-8 grid gap-6 md:grid-cols-2 lg:grid-cols-3">
			<!-- Tracking Básico -->
			<Card.Root>
				<Card.Header>
					<Card.Title class="flex items-center gap-2">
						<Icon icon="mdi:cursor-default-click" />
						Tracking Básico
					</Card.Title>
				</Card.Header>
				<Card.Content class="space-y-4">
					<p class="text-muted-foreground text-sm">
						Exemplo básico de tracking de ações do usuário.
					</p>
					<button
						onclick={handleBasicTracking}
						class="w-full rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
					>
						Clique Aqui ({clickCount})
					</button>
					<code class="block rounded bg-gray-100 p-2 text-xs">
						trackAction('demo-basic-button-click')
					</code>
				</Card.Content>
			</Card.Root>

			<!-- Discord Notification -->
			<Card.Root>
				<Card.Header>
					<Card.Title class="flex items-center gap-2">
						<Icon icon="mdi:discord" />
						Discord Notification
					</Card.Title>
				</Card.Header>
				<Card.Content class="space-y-4">
					<p class="text-muted-foreground text-sm">
						Envie notificações para Discord (se configurado).
					</p>
					<button
						onclick={handleDiscordNotification}
						class="w-full rounded-md border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50"
					>
						Testar Discord
					</button>
					<code class="block rounded bg-gray-100 p-2 text-xs">
						trackAction('action', {`{ discord: true }`})
					</code>
				</Card.Content>
			</Card.Root>

			<!-- Feature Usage -->
			<Card.Root>
				<Card.Header>
					<Card.Title class="flex items-center gap-2">
						<Icon icon="mdi:feature-search" />
						Uso de Features
					</Card.Title>
				</Card.Header>
				<Card.Content class="space-y-4">
					<p class="text-muted-foreground text-sm">
						Rastreie o uso de funcionalidades específicas.
					</p>
					<Button onclick={handleFeatureUsage} variant="secondary" class="w-full">
						Testar Feature
					</Button>
					<code class="block rounded bg-gray-100 p-2 text-xs">
						trackAction('demo-feature-usage')
					</code>
				</Card.Content>
			</Card.Root>
		</div>

		<!-- Discord Examples -->
		<Card.Root class="mb-8">
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Icon icon="mdi:discord" />
					Exemplos Discord (Server-Side)
				</Card.Title>
			</Card.Header>
			<Card.Content>
				<p class="text-muted-foreground mb-4 text-sm">
					Demonstre notificações Discord para eventos importantes. Configure DISCORD_WEBHOOK_URL no
					servidor.
				</p>
				<div class="grid gap-4 md:grid-cols-3">
					<Button onclick={handleUserSignup} variant="default" class="w-full">
						🎉 Novo Usuário
					</Button>
					<Button onclick={handleImportantAction} variant="destructive" class="w-full">
						🚨 Ação Importante
					</Button>
					<Button onclick={handleDiscordNotification} variant="outline" class="w-full">
						📢 Notificação Custom
					</Button>
				</div>
				<div class="mt-4 rounded bg-gray-50 p-3">
					<code class="text-xs">
						trackAction('user-signup', {`{ discord: true, discordMessage: '🎉 New user!' }`})
					</code>
				</div>
			</Card.Content>
		</Card.Root>

		<!-- Exemplo de Formulário -->
		<Card.Root class="mb-8">
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Icon icon="mdi:form-select" />
					Tracking de Formulários
				</Card.Title>
			</Card.Header>
			<Card.Content>
				<p class="text-muted-foreground mb-4 text-sm">
					Rastreie interações de formulário manualmente. Teste preenchendo os campos abaixo:
				</p>
				<div class="grid gap-4 md:grid-cols-2">
					<div>
						<label for="demo-name" class="mb-1 block text-sm font-medium">Nome</label>
						<input
							id="demo-name"
							type="text"
							bind:value={formData.name}
							oninput={handleFormInput}
							class="w-full rounded-md border px-3 py-2"
							placeholder="Seu nome"
						/>
					</div>
					<div>
						<label for="demo-email" class="mb-1 block text-sm font-medium">Email</label>
						<input
							id="demo-email"
							type="email"
							bind:value={formData.email}
							oninput={handleFormInput}
							class="w-full rounded-md border px-3 py-2"
							placeholder="<EMAIL>"
						/>
					</div>
				</div>
				<div class="mt-4">
					<Button onclick={handleFormSubmit} class="w-full">Submeter Formulário</Button>
				</div>
			</Card.Content>
		</Card.Root>

		<!-- Documentação -->
		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Icon icon="mdi:book-open" />
					Documentação e Próximos Passos
				</Card.Title>
			</Card.Header>
			<Card.Content class="space-y-4">
				<div class="grid gap-4 md:grid-cols-2">
					<div>
						<h4 class="mb-2 font-semibold">📚 Documentação Completa</h4>
						<p class="text-muted-foreground mb-2 text-sm">Consulte o README completo em:</p>
						<code class="block rounded bg-gray-100 p-2 text-xs"> src/lib/tracking/README.md </code>
					</div>
					<div>
						<h4 class="mb-2 font-semibold">🔧 Configuração</h4>
						<p class="text-muted-foreground mb-2 text-sm">Personalize o tracking em:</p>
						<code class="block rounded bg-gray-100 p-2 text-xs"> src/routes/+layout.svelte </code>
					</div>
				</div>

				<div class="mt-6 rounded-lg bg-blue-50 p-4">
					<h4 class="mb-2 font-semibold text-blue-800">💡 Nova API Simplificada</h4>
					<ul class="space-y-1 text-sm text-blue-700">
						<li>• <strong>2 métodos principais:</strong> trackAction() e navegação automática</li>
						<li>• <strong>Server-side:</strong> Discord via DISCORD_WEBHOOK_URL no servidor</li>
						<li>• <strong>Simples:</strong> Sem configurações complexas, foco no essencial</li>
						<li>• <strong>Seguro:</strong> Tudo processado via /api/actions endpoint</li>
					</ul>
				</div>
			</Card.Content>
		</Card.Root>
	</div>
</div>
