// Error reporting with Discord integration
// Uses centralized /api/events endpoint for simplified Discord communication

import { dev, browser } from '$app/environment';

interface GroupedError {
    key: string;
    count: number;
    firstOccurrence: number;
    lastReport: number;
}

/**
 * Manages error reporting with Discord integration
 * Groups similar errors to prevent spam, sends via centralized events API
 * Includes automatic cleanup to prevent memory leaks
 */
export class DiscordReporter {
    private readonly groupingWindow = 5 * 60 * 1000; // 5 minutes in ms
    private readonly maxAge = 24 * 60 * 60 * 1000; // 24 hours in ms
    private readonly maxMapSize = 1000; // Trigger cleanup when Map exceeds this size
    private errorGroups = new Map<string, GroupedError>();

    constructor() {
        // No longer needs webhook URL - uses centralized endpoint
    }

    /**
     * Creates a unique key for error grouping
     * Groups by: type + function + route + message
     */
    private createErrorKey(type: string, functionName: string, route: string, message: string): string {
        return `${type}:${functionName}:${route}:${message}`;
    }

    /**
     * Cleans up old error entries to prevent memory leaks
     * Removes entries that haven't been reported in the last 24 hours
     */
    private cleanupOldErrors(): void {
        const now = Date.now();
        let removedCount = 0;

        for (const [key, error] of this.errorGroups.entries()) {
            if (now - error.lastReport > this.maxAge) {
                this.errorGroups.delete(key);
                removedCount++;
            }
        }

        // Log cleanup activity in development
        if (removedCount > 0 && typeof console !== 'undefined') {
            console.log(`🧹 [Error Reporter] Cleaned up ${removedCount} old error entries. Map size: ${this.errorGroups.size}`);
        }
    }

    /**
     * Checks if error should be reported based on grouping rules
     * Includes automatic cleanup to prevent memory leaks
     */
    private shouldReport(errorKey: string): boolean {
        const now = Date.now();

        // Perform cleanup when Map gets too large
        if (this.errorGroups.size > this.maxMapSize) {
            this.cleanupOldErrors();
        }

        const existing = this.errorGroups.get(errorKey);

        if (!existing) {
            // First occurrence - always report
            this.errorGroups.set(errorKey, {
                key: errorKey,
                count: 1,
                firstOccurrence: now,
                lastReport: now
            });
            return true;
        }

        // Check if grouping window has passed
        if (now - existing.lastReport > this.groupingWindow) {
            // Update last report time and increment count
            existing.lastReport = now;
            existing.count += 1;
            return true;
        }

        // Within grouping window - don't report
        existing.count += 1;
        return false;
    }

    /**
     * Gets emoji and color for error type
     */
    private getErrorStyle(type: string): { emoji: string; color: number } {
        const styles = {
            HTTP_ERROR: { emoji: '🔴', color: 0xff0000 },      // Red
            COMPONENT_ERROR: { emoji: '🟡', color: 0xffa500 }, // Orange  
            TIMEOUT_ERROR: { emoji: '🟠', color: 0xff8c00 },   // Dark Orange
            FATAL_ERROR: { emoji: '💀', color: 0x8b0000 }      // Dark Red
        };

        return styles[type as keyof typeof styles] || { emoji: '⚪', color: 0x808080 };
    }

    /**
     * Formats error for Discord embed
     */
    private formatErrorEmbed(error: any): any {
        const style = this.getErrorStyle(error.type);
        const grouped = this.errorGroups.get(
            this.createErrorKey(error.type, error.details.functionName || 'unknown', error.context.route, error.details.message)
        );

        return {
            title: `${style.emoji} ${error.type}`,
            color: style.color,
            fields: [
                {
                    name: '📍 Route',
                    value: error.context.route,
                    inline: true
                },
                {
                    name: '🔧 Function',
                    value: error.details.functionName || 'unknown',
                    inline: true
                },
                {
                    name: '🕐 Time',
                    value: error.context.timestamp,
                    inline: true
                },
                {
                    name: '🌐 Browser',
                    value: error.context.userAgent,
                    inline: true
                },
                ...(error.details.statusCode ? [{
                    name: '📊 Status Code',
                    value: error.details.statusCode.toString(),
                    inline: true
                }] : []),
                ...(grouped && grouped.count > 1 ? [{
                    name: '🔄 Occurrences',
                    value: `${grouped.count} times`,
                    inline: true
                }] : []),
                {
                    name: '💬 Message',
                    value: '```\n' + error.details.message + '\n```',
                    inline: false
                }
            ],
            timestamp: error.context.timestamp
        };
    }

    /**
     * Sends error report via centralized events API
     * Only reports in production unless force flag is set
     * @param error - The error to report
     * @param force - Force reporting in dev mode (default: false)
     */
    public async reportError(error: any, force: boolean = false): Promise<void> {
        // Environment check
        if (dev && !force) {
            console.log('🔍 [Error Reporter - DEV]', error);
            return;
        }

        // Skip if not in browser (server-side doesn't need to report via fetch)
        if (!browser) {
            console.log('🔍 [Error Reporter - SERVER] Skipping Discord report on server-side');
            return;
        }

        // Check if should report based on grouping
        const errorKey = this.createErrorKey(
            error.type,
            error.details.functionName || 'unknown',
            error.context.route,
            error.details.message
        );

        if (!this.shouldReport(errorKey)) {
            return; // Skip reporting due to grouping
        }

        try {
            const embed = this.formatErrorEmbed(error);

            // Send via centralized events API (client-side only)
            const response = await fetch('/api/events', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ embed })
            });

            if (!response.ok) {
                throw new Error(`Events API failed: ${response.status}`);
            }

            console.log('📢 Error report sent via events API');

        } catch (apiError) {
            // Only log API failures in dev
            if (dev) {
                console.error('Failed to send error via events API:', apiError);
            }
        }
    }
}