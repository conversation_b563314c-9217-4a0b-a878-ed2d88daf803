// errorBoundaryReport.ts

import { reportError, ErrorType } from './index';

interface ErrorBoundaryOptions {
    /** Name of the component for error reporting */
    componentName?: string;
    /** Whether to report errors in development mode */
    enableInDev?: boolean;
    /** Custom error handler (called after reporting) */
    onError?: (error: Error, element: HTMLElement) => void;
    /** Whether to prevent the error from bubbling up */
    preventDefault?: boolean;
}

interface ErrorBoundaryReturn {
    update?: (options: ErrorBoundaryOptions) => void;
    destroy: () => void;
}

/**
 * Svelte action that automatically reports component errors
 * Captures JavaScript errors within the element and its children
 * 
 * @param node - HTML element to monitor for errors
 * @param options - Configuration options
 * @returns Svelte action object with destroy method
 * 
 * @example
 * ```svelte
 * <!-- Basic usage -->
 * <div use:errorBoundary>
 *   <!-- Component content that might error -->
 * </div>
 * 
 * <!-- With component name -->
 * <div use:errorBoundary={{ componentName: 'UserProfile' }}>
 *   <UserProfileCard />
 * </div>
 * 
 * <!-- With custom error handling -->
 * <div use:errorBoundary={{ 
 *   componentName: 'CriticalSection',
 *   onError: (error, element) => {
 *     // Custom handling after reporting
 *     element.classList.add('error-state');
 *   }
 * }}>
 *   <CriticalComponent />
 * </div>
 * ```
 */
export function errorBoundary(
    node: HTMLElement,
    options: ErrorBoundaryOptions = {}
): ErrorBoundaryReturn {

    let currentOptions = {
        componentName: 'UnknownComponent',
        enableInDev: false,
        preventDefault: true,
        ...options
    };

    /**
     * Handles JavaScript errors within the monitored element
     */
    function handleError(event: ErrorEvent): void {
        const error = event.error || new Error(event.message || 'Unknown component error');

        // Extract component name from data attribute or options
        const componentName = node.dataset.componentName ||
            currentOptions.componentName ||
            'UnknownComponent';

        // Report the error
        reportError(ErrorType.COMPONENT_ERROR, {
            functionName: componentName,
            message: `Component error: ${error.message || event.message}`,
            originalError: error
        }, currentOptions.enableInDev);

        // Call custom error handler if provided
        if (currentOptions.onError) {
            try {
                currentOptions.onError(error, node);
            } catch (handlerError) {
                console.warn('Error boundary custom handler failed:', handlerError);
            }
        }

        // Prevent error from bubbling if configured
        if (currentOptions.preventDefault) {
            event.preventDefault();
            event.stopPropagation();
        }
    }

    /**
     * Handles unhandled promise rejections within the component
     */
    function handlePromiseRejection(event: PromiseRejectionEvent): void {
        // Only handle if the rejection originated from our monitored element
        // This is a limitation - we can't perfectly scope promise rejections to elements
        if (!node.contains(event.target as Node) && event.target !== node) {
            return;
        }

        const error = event.reason instanceof Error ?
            event.reason :
            new Error(String(event.reason));

        const componentName = node.dataset.componentName ||
            currentOptions.componentName ||
            'UnknownComponent';

        // Report the promise rejection
        reportError(ErrorType.COMPONENT_ERROR, {
            functionName: `${componentName}_Promise`,
            message: `Unhandled promise rejection: ${error.message}`,
            originalError: error
        }, currentOptions.enableInDev);

        // Call custom error handler if provided
        if (currentOptions.onError) {
            try {
                currentOptions.onError(error, node);
            } catch (handlerError) {
                console.warn('Error boundary promise handler failed:', handlerError);
            }
        }

        // Prevent default handling if configured
        if (currentOptions.preventDefault) {
            event.preventDefault();
        }
    }

    // Attach event listeners
    node.addEventListener('error', handleError, true); // Use capture phase
    window.addEventListener('unhandledrejection', handlePromiseRejection);

    return {
        /**
         * Updates the error boundary options
         */
        update(newOptions: ErrorBoundaryOptions) {
            currentOptions = { ...currentOptions, ...newOptions };
        },

        /**
         * Cleanup function called when element is destroyed
         */
        destroy() {
            node.removeEventListener('error', handleError, true);
            window.removeEventListener('unhandledrejection', handlePromiseRejection);
        }
    };
}

/**
 * Helper function to create error boundary with pre-configured options
 * Useful for consistent error boundaries across the app
 * 
 * @param defaultOptions - Default options for all error boundaries
 * @returns Configured error boundary action
 * 
 * @example
 * ```typescript
 * // Create app-wide error boundary config
 * export const appErrorBoundary = createErrorBoundary({
 *   enableInDev: true,
 *   preventDefault: true,
 *   onError: (error, element) => {
 *     element.classList.add('component-error');
 *   }
 * });
 * 
 * // Use in components
 * <div use:appErrorBoundary={{ componentName: 'MyComponent' }}>
 *   <MyComponent />
 * </div>
 * ```
 */
export function createErrorBoundary(defaultOptions: ErrorBoundaryOptions) {
    return function configurableErrorBoundary(
        node: HTMLElement,
        options: ErrorBoundaryOptions = {}
    ): ErrorBoundaryReturn {
        const mergedOptions = { ...defaultOptions, ...options };
        return errorBoundary(node, mergedOptions);
    };
}

/**
 * Pre-configured error boundary for development/testing
 * Enables reporting in dev mode and shows visual feedback
 */
export const devErrorBoundary = createErrorBoundary({
    enableInDev: true,
    preventDefault: true,
    onError: (error, element) => {
        // Add visual indicator in dev mode
        element.style.border = '2px solid red';
        element.style.backgroundColor = 'rgba(255, 0, 0, 0.1)';

        // Log detailed error info in dev
        console.group('🚨 Component Error Boundary');
        console.error('Error:', error);
        console.log('Element:', element);
        console.groupEnd();
    }
});