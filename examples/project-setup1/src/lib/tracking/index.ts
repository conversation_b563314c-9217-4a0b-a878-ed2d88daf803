// Simple tracking system - main API
// Follows the same pattern as the error reporting module

import { browser } from '$app/environment';
import { initializeTracking } from './tracker.svelte';
import type { TrackingConfig, TrackingOptions } from './types';

// Global tracker instance
let globalTracker: any = null;

/**
 * Initialize the tracking system
 * Should be called once in your root layout
 *
 * @param config - Optional tracking configuration
 *
 * @example
 * ```typescript
 * // In your +layout.svelte
 * import { initTracking } from '$lib/tracking';
 * import { onMount } from 'svelte';
 *
 * onMount(() => {
 *   initTracking();
 * });
 * ```
 */
export function initTracking(config: TrackingConfig = {}): void {
    // Prevent multiple initializations
    if (globalTracker) {
        console.warn('Tracking already initialized');
        return;
    }

    if (!browser) {
        console.warn('⚠️ Tracking can only be initialized in the browser');
        return;
    }

    // Create global tracker instance
    globalTracker = initializeTracking(config);
}

/**
 * Gets the global tracker instance
 * Must be called after initTracking()
 *
 * @returns Global tracker instance
 * @throws Error if not initialized
 */
export function getGlobalTracker() {
    if (!globalTracker) {
        throw new Error('Tracking not initialized. Call initTracking() first.');
    }
    return globalTracker;
}

/**
 * Track user action - main tracking function
 *
 * @param actionName - Name of the action (e.g., 'login-button-click', 'form-submit')
 * @param options - Optional tracking options (Discord notification, custom message)
 *
 * @example
 * ```typescript
 * import { trackAction } from '$lib/tracking';
 *
 * // Track button click (normal)
 * trackAction('header-login-button');
 *
 * // Track with Discord notification
 * trackAction('user-signup', { discord: true });
 *
 * // Track with custom Discord message
 * trackAction('user-signup', {
 *   discord: true,
 *   discordMessage: '🎉 New user signed up!'
 * });
 * ```
 */
export function trackAction(actionName: string, options?: TrackingOptions): void {
    try {
        const tracker = getGlobalTracker();
        tracker.trackAction(actionName, options);
    } catch (error) {
        console.warn('Failed to track action:', error);
    }
}

/**
 * Navigation tracking is automatic - no manual calls needed
 * The system automatically tracks:
 * - Page visits
 * - Time spent on each page
 * - Page transitions
 */

// Re-exports for advanced usage
export { getTracker, initializeTracking } from './tracker.svelte';

// Export types for TypeScript users
export type {
    TrackingConfig,
    TrackingAction,
    TrackingNavigation,
    TrackingOptions
} from './types';

// Export Discord utilities for advanced usage
export { TrackingDiscordReporter } from './discord';