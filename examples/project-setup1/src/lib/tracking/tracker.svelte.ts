// Simple tracking system - focused on core functionality

import { browser } from '$app/environment';
import type { TrackingConfig, TrackingAction, TrackingNavigation, TrackingOptions } from './types';

/**
 * Simple tracking system - tracks actions and navigation
 * Follows the same pattern as the error reporting module
 */
export class SimpleTracker {
    private config: TrackingConfig;
    private currentPage: string | null = null;
    private pageStartTime: number | null = null;
    private userId: string;
    private actionQueue: TrackingAction[] = [];
    private navigationQueue: TrackingNavigation[] = [];
    private flushTimer: number | null = null;
    private actionCounts: Map<string, number> = new Map();

    constructor(config: TrackingConfig = {}) {
        this.config = {
            projectId: 'default-project',
            apiUrl: '/api/actions',
            enabled: true,
            batchSize: 10,
            flushInterval: 30000, // 30 seconds
            ...config
        };

        // Generate or get user ID
        this.userId = this.getUserId();

        if (browser && this.config.enabled) {
            this.initialize();
        }
    }

    /**
     * Generate or retrieve user ID from localStorage
     */
    private getUserId(): string {
        if (!browser) return 'server-user';

        let userId = localStorage.getItem('tracking-user-id');
        if (!userId) {
            userId = crypto.randomUUID();
            localStorage.setItem('tracking-user-id', userId);
        }
        return userId;
    }

    /**
     * Initialize tracking system
     */
    private initialize(): void {
        try {
            this.setupNavigationTracking();
            this.startFlushTimer();
            console.log('🎯 Simple tracking initialized');
        } catch (error) {
            console.error('❌ Failed to initialize tracking:', error);
        }
    }

    /**
     * Setup navigation tracking
     */
    private setupNavigationTracking(): void {
        // Track initial page
        this.trackPageEnter(window.location.pathname);

        // Listen for navigation changes
        let previousPath = window.location.pathname;

        // Use popstate for back/forward navigation
        window.addEventListener('popstate', () => {
            const currentPath = window.location.pathname;
            if (currentPath !== previousPath) {
                this.trackPageExit(previousPath);
                this.trackPageEnter(currentPath);
                previousPath = currentPath;
            }
        });

        // Use beforeunload for page exit
        window.addEventListener('beforeunload', () => {
            if (this.currentPage) {
                this.trackPageExit(this.currentPage);
                this.flushSync(); // Synchronous flush on page exit
            }
        });
    }

    /**
     * Start flush timer
     */
    private startFlushTimer(): void {
        if (this.flushTimer) return;

        this.flushTimer = window.setInterval(() => {
            this.flush();
        }, this.config.flushInterval || 30000);
    }

    /**
     * Track page enter
     */
    private trackPageEnter(path: string): void {
        try {
            // End previous page if exists
            if (this.currentPage && this.pageStartTime) {
                this.trackPageExit(this.currentPage);
            }

            // Start new page tracking
            this.currentPage = path;
            this.pageStartTime = Date.now();

            console.log(`📄 Page enter tracked: ${path}`);
        } catch (error) {
            console.error('❌ Failed to track page enter:', error);
        }
    }

    /**
     * Track page exit
     */
    private trackPageExit(path: string): void {
        try {
            if (!this.pageStartTime) return;

            const timeSpent = Date.now() - this.pageStartTime;
            const enteredAt = new Date(this.pageStartTime).toISOString();
            const exitedAt = new Date().toISOString();

            const navigation: TrackingNavigation = {
                user_id: this.userId,
                page_path: path,
                time_spent: timeSpent,
                entered_at: enteredAt,
                exited_at: exitedAt
            };

            this.navigationQueue.push(navigation);
            console.log(`📄 Page exit tracked: ${path} (${timeSpent}ms)`);
        } catch (error) {
            console.error('❌ Failed to track page exit:', error);
        }
    }

    /**
     * Track user action with debouncing and optional Discord notification
     */
    public trackAction(actionName: string, options?: TrackingOptions): void {
        try {
            if (!this.config.enabled) return;

            // Debounce same action within 2 seconds (including Discord notifications)
            const now = Date.now();
            const lastAction = this.actionCounts.get(actionName) || 0;
            if (now - lastAction < 2000) {
                console.log(`⚠️ Action "${actionName}" debounced`);
                return;
            }

            this.actionCounts.set(actionName, now);

            // Send important Discord notifications immediately (independent of PocketBase)
            if (options?.discordImportant) {
                // Fire and forget - don't wait for Discord response
                this.sendDiscordNotification(actionName, options.discordMessage).catch(error => {
                    console.error('❌ Discord notification failed (non-blocking):', error);
                });
            }

            const action: TrackingAction = {
                action_name: actionName,
                user_id: this.userId,
                page_path: this.currentPage || window.location.pathname,
                timestamp: new Date().toISOString()
            };

            this.actionQueue.push(action);
            console.log(`🎯 Action tracked: ${actionName}`);

            // Auto-flush if queue is full
            if (this.actionQueue.length >= (this.config.batchSize || 10)) {
                this.flush();
            }
        } catch (error) {
            console.error('❌ Failed to track action:', error);
        }
    }

    /**
     * Send immediate Discord notification for important actions (fast & independent)
     */
    private async sendDiscordNotification(actionName: string, customMessage?: string): Promise<void> {
        try {
            const message = customMessage || `🎯 Important action: ${actionName}`;

            // Fast timeout for Discord notifications
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5s timeout

            const response = await fetch('/api/events', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message,
                    user_id: this.userId,
                    page_path: this.currentPage || window.location.pathname,
                    action_name: actionName
                }),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            console.log(`📢 Discord notification sent for: ${actionName}`);
        } catch (error) {
            // Log but don't throw - Discord failures should never block tracking
            console.error('❌ Discord notification failed:', error);
        }
    }

    /**
     * Flush queued data to server (independent of Discord notifications)
     */
    public async flush(): Promise<void> {
        if (!this.config.enabled) return;

        const actions = [...this.actionQueue];
        const navigations = [...this.navigationQueue];

        if (actions.length === 0 && navigations.length === 0) return;

        // Clear queues immediately to prevent blocking
        this.actionQueue = [];
        this.navigationQueue = [];

        try {
            // Send to server with shorter timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout

            const response = await fetch(this.config.apiUrl || '/api/actions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    actions,
                    navigations
                }),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            console.log(`📤 Flushed ${actions.length} actions, ${navigations.length} navigations`);
        } catch (error) {
            console.error('❌ Failed to flush tracking data (non-blocking):', error);

            // Only re-add to queue if it's a temporary network issue, not a server error
            if (error instanceof TypeError || (error as any).name === 'AbortError') {
                console.log('🔄 Re-queuing data due to network issue');
                this.actionQueue.unshift(...actions);
                this.navigationQueue.unshift(...navigations);
            } else {
                console.log('⚠️ Discarding data due to server error to prevent memory leak');
            }
        }
    }

    /**
     * Synchronous flush for page unload
     */
    private flushSync(): void {
        try {
            if (!this.config.enabled) return;

            const actions = [...this.actionQueue];
            const navigations = [...this.navigationQueue];

            if (actions.length === 0 && navigations.length === 0) return;

            // Use sendBeacon for synchronous sending
            const data = JSON.stringify({ actions, navigations });
            navigator.sendBeacon(this.config.apiUrl || '/api/actions', data);

            console.log(`📤 Sync flushed ${actions.length} actions, ${navigations.length} navigations`);
        } catch (error) {
            console.error('❌ Failed to sync flush tracking data:', error);
        }
    }

    /**
     * Clean shutdown
     */
    public async destroy(): Promise<void> {
        try {
            // Track final page exit
            if (this.currentPage) {
                this.trackPageExit(this.currentPage);
            }

            // Flush remaining events
            await this.flush();

            // Clear timer
            if (this.flushTimer) {
                clearInterval(this.flushTimer);
                this.flushTimer = null;
            }

            console.log('🎯 Simple tracking destroyed');
        } catch (error) {
            console.error('❌ Failed to destroy tracking:', error);
        }
    }
}

// Singleton instance for easy access
let trackerInstance: SimpleTracker | null = null;

/**
 * Get the global tracker instance
 */
export function getTracker(config?: Partial<TrackingConfig>): SimpleTracker {
    if (!trackerInstance && browser) {
        trackerInstance = new SimpleTracker(config);
    }
    return trackerInstance!;
}

/**
 * Initialize tracking with custom config
 */
export function initializeTracking(config?: Partial<TrackingConfig>): SimpleTracker {
    if (trackerInstance) {
        console.warn('⚠️ Tracking already initialized');
        return trackerInstance;
    }

    trackerInstance = new SimpleTracker(config);
    return trackerInstance;
}