// Simple tracking types - focused on core functionality

export interface TrackingAction {
    action_name: string;
    user_id: string;
    page_path: string;
    timestamp: string;
}

export interface TrackingNavigation {
    user_id: string;
    page_path: string;
    time_spent: number;
    entered_at: string;
    exited_at: string;
}

export interface TrackingConfig {
    projectId?: string;
    apiUrl?: string;
    enabled?: boolean;
    batchSize?: number;
    flushInterval?: number;
}

export interface TrackingOptions {
    discord?: boolean;
    discordMessage?: string;
    discordImportant?: boolean;
}