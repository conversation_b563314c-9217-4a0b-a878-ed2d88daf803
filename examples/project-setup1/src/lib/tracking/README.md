# Simple Tracking System

A simple and focused user behavior tracking system for SvelteKit applications with PocketBase integration.

## Philosophy

This tracking system follows the same simple pattern as the error reporting module:
- **2 core methods**: `trackAction()` and automatic navigation tracking
- **Server-side only**: All PocketBase operations via `/api/actions` endpoint
- **Simple configuration**: Minimal setup required
- **Focused functionality**: Track what matters most

## Quick Start

### 1. Initialize Tracking

```typescript
// In your root +layout.svelte
import { initTracking } from '$lib/tracking';
import { onMount } from 'svelte';

onMount(() => {
  initTracking({
    projectId: 'my-app',
    apiUrl: '/api/actions'
  });
});
```

### 2. Track User Actions

```typescript
import { trackAction } from '$lib/tracking';

// Track button clicks (normal)
trackAction('header-login-button');

// Track with Discord notification
trackAction('user-signup', { discord: true });

// Track with custom Discord message
trackAction('user-signup', {
  discord: true,
  discordMessage: '🎉 New user just signed up!'
});

// Track form submissions
trackAction('contact-form-submit');

// Track feature usage
trackAction('export-data-feature');
```

### 3. Navigation Tracking (Automatic)

Navigation tracking happens automatically:
- Page visits are tracked when users enter/exit pages
- Time spent on each page is calculated
- All data is sent to PocketBase via server endpoint

## Core Methods

### trackAction(actionName, options?)

The main tracking function for all user interactions:

```typescript
// Basic action tracking
trackAction('login-attempt-button');
trackAction('form-contact-submit');
trackAction('feature-export-used');

// Action tracking with Discord notification
trackAction('user-signup', { discord: true });
trackAction('payment-completed', {
  discord: true,
  discordMessage: '💰 Payment completed successfully!'
});
```

### Discord Integration (Optional)

Send important actions to Discord for real-time monitoring (server-side only):

```typescript
// No client-side configuration needed!
// Discord webhook is configured server-side via environment variables

// Track actions with Discord notifications
trackAction('user-signup', { discord: true });
trackAction('error-occurred', {
  discord: true,
  discordMessage: '🚨 Critical error detected!'
});
```

### Automatic Navigation Tracking

- **Page Enter**: Tracked when user visits a page
- **Page Exit**: Tracked when user leaves a page
- **Time Calculation**: Automatically calculates time spent
- **Server Storage**: All data saved via `/api/actions` endpoint

## Configuration

### Basic Setup

```typescript
const config = {
  projectId: 'my-project',    // Optional, defaults to 'default-project'
  apiUrl: '/api/actions',     // Optional, defaults to '/api/actions'
  enabled: true,              // Optional, defaults to true
  batchSize: 10,              // Optional, defaults to 10
  flushInterval: 30000        // Optional, defaults to 30 seconds
};

initTracking(config);
```

### Minimal Setup

```typescript
// Just initialize with defaults
initTracking();
```

## Data Structure

### Actions Table (tracking_actions)

```sql
CREATE TABLE tracking_actions (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  action_name TEXT NOT NULL,
  page_path TEXT,
  count INTEGER DEFAULT 1,
  first_executed DATETIME,
  last_executed DATETIME,
  created DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Navigation Table (tracking_navigation)

```sql
CREATE TABLE tracking_navigation (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  page_path TEXT NOT NULL,
  time_spent INTEGER NOT NULL,
  entered_at DATETIME NOT NULL,
  exited_at DATETIME NOT NULL,
  created DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Server Endpoint

The `/api/actions/+server.ts` endpoint handles all PocketBase operations and Discord notifications:

```typescript
// POST /api/actions
{
  "actions": [
    {
      "action_name": "login-button-click",
      "user_id": "user-123",
      "page_path": "/login",
      "timestamp": "2024-01-01T12:00:00Z"
    }
  ],
  "navigations": [
    {
      "user_id": "user-123",
      "page_path": "/dashboard",
      "time_spent": 45000,
      "entered_at": "2024-01-01T12:00:00Z",
      "exited_at": "2024-01-01T12:00:45Z"
    }
  ],
  "discordActions": [
    {
      "action_name": "user-signup",
      "user_id": "user-123",
      "page_path": "/signup",
      "custom_message": "🎉 New user signed up!",
      "timestamp": "2024-01-01T12:00:00Z"
    }
  ]
}
```

### Server-Side Processing

1. **PocketBase Storage**: All actions and navigations are saved to PocketBase
2. **Discord Notifications**: Discord actions are processed server-side using environment webhook URL
3. **Security**: Webhook URL never exposed to client-side
4. **Reliability**: Discord failures don't affect PocketBase storage

## Features

### Action Debouncing

Prevents spam clicks from breaking the API:
- Same action within 2 seconds is ignored
- Protects against accidental double-clicks
- Maintains data quality

### Batch Processing

Efficient data transmission:
- Actions are queued and sent in batches
- Configurable batch size (default: 10)
- Automatic flush on page unload

### User Identification

Automatic user ID generation:
- Stored in localStorage as 'tracking-user-id'
- Persistent across sessions
- Generated using crypto.randomUUID()

## Usage Examples

### Button Tracking

```svelte
<script>
  import { trackAction } from '$lib/tracking';

  function handleLogin() {
    trackAction('login-button-click');
    // ... login logic
  }

  function handleSignup() {
    // Track with Discord notification for important events
    trackAction('user-signup', {
      discord: true,
      discordMessage: '🎉 New user signed up!'
    });
    // ... signup logic
  }
</script>

<button on:click={handleLogin}>Login</button>
<button on:click={handleSignup}>Sign Up</button>
```

### Form Tracking

```svelte
<script>
  import { trackAction } from '$lib/tracking';

  function handleSubmit() {
    trackAction('contact-form-submit');
    // ... form logic
  }

  function handleError() {
    trackAction('contact-form-error');
  }
</script>

<form on:submit={handleSubmit}>
  <!-- form fields -->
</form>
```

### Feature Tracking

```svelte
<script>
  import { trackAction } from '$lib/tracking';

  function exportData() {
    trackAction('export-feature-used');
    // ... export logic
  }
</script>

<button on:click={exportData}>
  Export Data
</button>
```

## API Reference

### Core Functions

- `initTracking(config?)` - Initialize tracking system
- `trackAction(actionName, options?)` - Track user action with optional Discord notification
- `getGlobalTracker()` - Get tracker instance (advanced)
- `TrackingDiscordReporter` - Discord integration class (advanced)

### Types

```typescript
interface TrackingConfig {
  projectId?: string;
  apiUrl?: string;
  enabled?: boolean;
  batchSize?: number;
  flushInterval?: number;
}

interface TrackingOptions {
  discord?: boolean;
  discordMessage?: string;
}

interface TrackingAction {
  action_name: string;
  user_id: string;
  page_path: string;
  timestamp: string;
}

interface TrackingNavigation {
  user_id: string;
  page_path: string;
  time_spent: number;
  entered_at: string;
  exited_at: string;
}
```

## Best Practices

### Action Naming

Use descriptive, consistent names:

```typescript
// Good
trackAction('header-login-button');
trackAction('sidebar-menu-toggle');
trackAction('form-newsletter-submit');

// Avoid
trackAction('click');
trackAction('button1');
trackAction('action');
```

### When to Track

Track meaningful user interactions:
- Button clicks that trigger actions
- Form submissions
- Feature usage
- Important navigation events

Don't track:
- Every mouse movement
- Scroll events (unless specific milestones)
- Hover events
- Passive interactions

### Performance

- Actions are automatically batched
- Navigation tracking is lightweight
- Server-side processing prevents client bloat
- Debouncing prevents spam

## Discord Integration

### Setup Discord Webhook (Server-Side)

1. Create a Discord webhook in your server
2. Copy the webhook URL
3. Set environment variable on your server:

```bash
# .env file
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/YOUR_WEBHOOK_URL
```

**Important**: Discord webhook is configured server-side only for security!

### Using Discord Notifications

```typescript
// Important user actions
trackAction('user-signup', { discord: true });
trackAction('payment-completed', { discord: true });

// Custom messages for context
trackAction('error-occurred', {
  discord: true,
  discordMessage: '🚨 Critical error in payment system!'
});

trackAction('milestone-reached', {
  discord: true,
  discordMessage: '🎉 1000th user signed up today!'
});
```

### Discord Message Format

Discord messages include:
- 🎯 **Action name** - What the user did
- 👤 **User ID** (shortened) - Who performed the action
- 📄 **Page path** - Where it happened
- ⏰ **Timestamp** - When it occurred
- 💬 **Custom message** (if provided)

### Development Mode

Discord notifications are disabled in development by default. To enable:

```typescript
import { TrackingDiscordReporter } from '$lib/tracking';

// Enable Discord in development
TrackingDiscordReporter.enableInDev();

// Disable Discord in development
TrackingDiscordReporter.disableInDev();
```