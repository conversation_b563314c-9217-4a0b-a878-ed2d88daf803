// Examples of how to use the updated tracking system
// This file shows how to track important actions that should send Discord notifications

import { getTracker } from './tracker.svelte';

/**
 * Track user registration - an important action that should send Discord notification
 */
export function trackUserRegistration(userId: string) {
    const tracker = getTracker();

    tracker.trackAction('user_registration', {
        discord: true,
        discordMessage: `🎉 New user registered: ${userId}`
    });
}

/**
 * Track user login - normal action, no Discord notification
 */
export function trackUserLogin(userId: string) {
    const tracker = getTracker();

    tracker.trackAction(`user_login_${userId}`);
}

/**
 * Track purchase completion - important action with Discord notification
 */
export function trackPurchaseComplete(userId: string, amount: number, productName: string) {
    const tracker = getTracker();

    tracker.trackAction('purchase_complete', {
        discord: true,
        discordMessage: `💰 Purchase completed! User: ${userId}, Product: ${productName}, Amount: $${amount}`
    });
}

/**
 * Track error occurrence - important action with Discord notification
 */
export function trackCriticalError(errorMessage: string, userId?: string) {
    const tracker = getTracker();

    tracker.trackAction('critical_error', {
        discord: true,
        discordMessage: `🚨 Critical error occurred: ${errorMessage}${userId ? ` (User: ${userId})` : ''}`
    });
}

/**
 * Track button click - normal action, no Discord notification
 */
export function trackButtonClick(buttonName: string) {
    const tracker = getTracker();

    tracker.trackAction(`button_click_${buttonName}`);
}

/**
 * Track feature usage - normal action, no Discord notification
 */
export function trackFeatureUsage(featureName: string) {
    const tracker = getTracker();

    tracker.trackAction(`feature_used_${featureName}`);
}

/**
 * Track subscription upgrade - important action with Discord notification
 */
export function trackSubscriptionUpgrade(userId: string, fromPlan: string, toPlan: string) {
    const tracker = getTracker();

    tracker.trackAction('subscription_upgrade', {
        discord: true,
        discordMessage: `📈 Subscription upgrade! User: ${userId}, From: ${fromPlan} → To: ${toPlan}`
    });
}
